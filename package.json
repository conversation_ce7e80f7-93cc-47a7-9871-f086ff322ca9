{"name": "mocky-digital", "version": "0.1.0", "private": true, "scripts": {"dev": "node server.js", "dev:config": "NODE_ENV=development next dev -c next.config.dev.js", "dev:debug": "DEBUG=* next dev", "dev:nextjs": "DEBUG=next:* next dev", "dev:trace": "NODE_OPTIONS='--trace-deprecation' next dev", "dev:rendering": "DEBUG=next:rendering* next dev", "dev:router": "DEBUG=next:router* next dev", "dev:build": "DEBUG=next:build* next dev", "dev:full": "DEBUG=next:*,react:* next dev", "build": "next build", "build:dev": "NODE_ENV=development next build -c next.config.dev.js", "start": "NODE_ENV=production node server.js", "lint": "next lint", "optimize-images": "node scripts/optimize-images.js", "clean": "rimraf .next node_modules/.cache", "reset": "rimraf .next node_modules package-lock.json && npm install", "dev:clean": "npm run clean && npm run dev", "build:prod": "NODE_ENV=production next build", "dev:fast": "NEXT_MINIMAL=1 NODE_OPTIONS='--max-old-space-size=4096' next dev", "dev:memory": "NODE_OPTIONS='--max-old-space-size=8192' next dev", "prisma:generate": "prisma generate", "prisma:push": "prisma db push", "prisma:seed": "ts-node --compiler-options {\"module\":\"CommonJS\"} prisma/seed.ts", "prisma:migrate": "prisma migrate dev", "prisma:seed:website": "ts-node scripts/seed-website-portfolio-prisma.ts", "db:setup": "npm run prisma:generate && npm run prisma:push && npm run prisma:seed", "db:migrate-seed-website": "bash scripts/migrate-and-seed.sh", "seed:storage": "node prisma/seed-storage.js", "verify:storage": "node scripts/verify-storage-config.js", "test:team": "node scripts/test-team-creation.js", "blog:generate": "ts-node scripts/scheduled-blog-post.ts --run-once", "blog:setup-cron": "bash scripts/setup-blog-automation.sh", "blog:setup-pm2": "node scripts/setup-blog-automation-pm2.js", "seo:scan": "node scripts/seo-scan.js", "seo:scan-all": "node scripts/seo-scan.js --all", "seo:setup-cron": "bash scripts/setup-seo-cron.sh"}, "dependencies": {"@aws-sdk/client-s3": "^3.772.0", "@aws-sdk/node-http-handler": "^3.374.0", "@aws-sdk/s3-request-presigner": "^3.772.0", "@aws-sdk/types": "^3.734.0", "@emailjs/browser": "^4.4.1", "@google-analytics/data": "^5.1.0", "@headlessui/react": "^2.2.0", "@heroicons/react": "^2.2.0", "@prisma/client": "^6.7.0", "@radix-ui/react-label": "^2.1.6", "@radix-ui/react-radio-group": "^1.2.4", "@radix-ui/react-slot": "^1.2.2", "@radix-ui/react-switch": "^1.2.4", "@tailwindcss/forms": "^0.5.10", "@tanstack/react-virtual": "^3.13.4", "@tiptap/extension-heading": "^2.11.5", "@tiptap/extension-image": "^2.11.5", "@tiptap/extension-link": "^2.11.5", "@tiptap/extension-placeholder": "^2.11.5", "@tiptap/react": "^2.11.5", "@tiptap/starter-kit": "^2.11.5", "@types/bcryptjs": "^3.0.0", "@types/dompurify": "^3.2.0", "@types/jsdom": "^21.1.7", "@types/node-cron": "^3.0.11", "@types/pg": "^8.15.0", "aos": "^2.3.4", "axios": "^1.9.0", "bcryptjs": "^3.0.2", "browser-image-compression": "^2.0.2", "chart.js": "^4.4.8", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^4.1.0", "dompurify": "^3.2.5", "dotenv": "^16.5.0", "framer-motion": "^10.18.0", "js-cookie": "^3.0.5", "jsdom": "^26.1.0", "lodash": "^4.17.21", "lucide-react": "^0.483.0", "mime-types": "^2.1.35", "next": "^15.2.4", "next-auth": "^4.24.11", "node-cron": "^4.0.3", "node-fetch": "^2.7.0", "openai": "^4.98.0", "p-queue": "^8.1.0", "pdfkit": "^0.17.1", "pdfkit-table": "^0.1.99", "pg": "^8.15.6", "plaiceholder": "^3.0.0", "prisma": "^6.7.0", "react": "18.2.0", "react-chartjs-2": "^5.3.0", "react-countup": "^6.5.3", "react-dom": "18.2.0", "react-dropzone": "^14.3.8", "react-error-boundary": "^5.0.0", "react-hot-toast": "^2.5.2", "react-icons": "^5.5.0", "react-intersection-observer": "^9.16.0", "react-type-animation": "^3.2.0", "react-window": "^1.8.11", "slugify": "^1.6.6", "swiper": "^11.2.5", "tailwind-merge": "^3.2.0", "zod": "^3.24.4"}, "devDependencies": {"@types/aos": "^3.0.7", "@types/dotenv": "^8.2.3", "@types/lodash": "^4.17.16", "@types/mime-types": "^2.1.4", "@types/node": "^20.17.25", "@types/node-fetch": "^2.6.12", "@types/pdfkit": "^0.13.9", "@types/react": "18.3.18", "@types/react-dom": "^18.2.19", "@types/uuid": "^10.0.0", "autoprefixer": "^10.4.21", "eslint": "^9.22.0", "eslint-config-next": "^15.2.4", "glob": "^10.3.10", "http-cache-semantics": "^4.1.1", "imagemin": "^9.0.0", "postcss": "^8.5.3", "rimraf": "^5.0.5", "sharp": "^0.33.5", "tailwindcss": "^3.4.17", "terser-webpack-plugin": "^5.3.14", "ts-node": "^10.9.2", "typescript": "^5.8.2", "uuid": "^9.0.1"}, "prisma": {"seed": "ts-node --compiler-options {\"module\":\"CommonJS\"} prisma/seed.ts"}}