/**
 * Utility functions for analytics tracking
 *
 * This file provides client-side tracking functions that integrate with:
 * 1. Facebook Pixel (browser-side)
 * 2. Meta Conversion API (server-side)
 * 3. Our custom event tracking system
 */

// Import types from our event tracking service
// These are only used for TypeScript type checking and will be removed in production
import type { EventData } from '@/services/eventTrackingService';

/**
 * Track a custom event with Facebook Pixel
 * @param eventName The name of the event to track
 * @param eventParams Optional parameters for the event
 * @param serverSide Whether to also send the event to the server-side API
 */
export const trackFacebookEvent = async (
  eventName: string,
  eventParams?: Record<string, any>,
  serverSide: boolean = false
) => {
  try {
    // Track with browser pixel
    if (typeof window !== 'undefined' && window.fbq) {
      window.fbq('track', eventName, eventParams);
      console.log(`[FB Pixel] Tracked event: ${eventName}`, eventParams);
    }

    // Also send to server-side API if requested
    if (serverSide && typeof window !== 'undefined') {
      try {
        const response = await fetch('/api/meta-pixel', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            event_name: eventName,
            event_source_url: window.location.href,
            custom_data: eventParams,
            user_data: {
              // Include any user data from eventParams that should be hashed
              // The server will hash these values
              ...(eventParams?.email && { email: eventParams.email }),
              ...(eventParams?.phone && { phone: eventParams.phone }),
              ...(eventParams?.firstName && { firstName: eventParams.firstName }),
              ...(eventParams?.lastName && { lastName: eventParams.lastName }),
              // Include Facebook browser parameters if available
              fbp: getCookie('_fbp'),
              fbc: getCookie('_fbc') || getURLParameter('fbclid'),
            },
          }),
        });

        if (!response.ok) {
          throw new Error(`Server responded with ${response.status}`);
        }

        const data = await response.json();

        if (data.warning) {
          console.warn(`[FB Conversion API] Warning: ${data.warning}`);
        } else {
          console.log(`[FB Conversion API] Tracked event: ${eventName}`);
        }
      } catch (serverError) {
        console.error('[FB Conversion API] Error tracking event:', serverError);
      }
    }
  } catch (error) {
    console.error('[FB Pixel] Error tracking event:', error);
  }
};

/**
 * Get a cookie value by name
 * @param name The name of the cookie
 * @returns The cookie value or empty string if not found
 */
function getCookie(name: string): string {
  if (typeof document === 'undefined') return '';

  const value = `; ${document.cookie}`;
  const parts = value.split(`; ${name}=`);
  if (parts.length === 2) return parts.pop()?.split(';').shift() || '';
  return '';
}

/**
 * Get a URL parameter value by name
 * @param name The name of the parameter
 * @returns The parameter value or empty string if not found
 */
function getURLParameter(name: string): string {
  if (typeof window === 'undefined') return '';

  const searchParams = new URLSearchParams(window.location.search);
  return searchParams.get(name) || '';
};

/**
 * Track a logo design form submission
 * @param packageName The name of the logo package
 * @param packagePrice The price of the logo package
 * @param formData The form data submitted by the user
 */
export const trackLogoFormSubmission = async (
  packageName: string,
  packagePrice: string,
  formData: {
    businessName: string;
    industry: string;
    logoType: string;
    slogan?: string;
    additionalInfo?: string;
    email?: string;
    phone?: string;
    firstName?: string;
    lastName?: string;
  }
) => {
  try {
    // Parse the price value
    const priceValue = parseInt(packagePrice.replace(/,/g, ''), 10);

    // Common event parameters
    const eventParams = {
      content_name: `${packageName} Logo Package`,
      content_category: 'Logo Design',
      value: priceValue,
      currency: 'KES',
      status: 'submitted',
      business_name: formData.businessName,
      industry: formData.industry,
      logo_type: formData.logoType,
      has_slogan: !!formData.slogan,
      has_additional_info: !!formData.additionalInfo,
      // Include user data for server-side tracking if available
      ...(formData.email && { email: formData.email }),
      ...(formData.phone && { phone: formData.phone }),
      ...(formData.firstName && { firstName: formData.firstName }),
      ...(formData.lastName && { lastName: formData.lastName }),
    };

    // Track as a standard Lead event (browser-side only)
    trackStandardEvent('Lead', eventParams);

    // Also track as a custom event for more detailed analysis (both browser and server-side)
    // This is a high-value conversion, so we use server-side tracking for reliability
    await trackFacebookEvent('LogoDesignSubmission', eventParams, true);

    console.log(`[FB Pixel] Tracked logo design submission: ${packageName} - KSH ${packagePrice}`);
  } catch (error) {
    console.error('[FB Pixel] Error tracking logo design submission:', error);
  }
};

/**
 * Track a standard Facebook Pixel event
 * @param eventName The name of the standard event to track
 * @param eventParams Optional parameters for the event
 * @param serverSide Whether to also send the event to the server-side API
 */
export const trackStandardEvent = async (
  eventName:
    | 'AddPaymentInfo'
    | 'AddToCart'
    | 'AddToWishlist'
    | 'CompleteRegistration'
    | 'Contact'
    | 'CustomizeProduct'
    | 'Donate'
    | 'FindLocation'
    | 'InitiateCheckout'
    | 'Lead'
    | 'Purchase'
    | 'Schedule'
    | 'Search'
    | 'StartTrial'
    | 'SubmitApplication'
    | 'Subscribe'
    | 'ViewContent',
  eventParams?: Record<string, any>,
  serverSide: boolean = false
) => {
  try {
    // Track with browser pixel
    if (typeof window !== 'undefined' && window.fbq) {
      window.fbq('track', eventName, eventParams);
      console.log(`[FB Pixel] Tracked standard event: ${eventName}`, eventParams);
    }

    // For high-value events, also send to server-side API if requested
    // This provides more reliable tracking for important conversions
    const highValueEvents = ['Purchase', 'Lead', 'CompleteRegistration', 'InitiateCheckout'];

    if ((serverSide || highValueEvents.includes(eventName)) && typeof window !== 'undefined') {
      try {
        const response = await fetch('/api/meta-pixel', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            event_name: eventName,
            event_source_url: window.location.href,
            custom_data: eventParams,
            user_data: {
              // Include any user data from eventParams that should be hashed
              ...(eventParams?.email && { email: eventParams.email }),
              ...(eventParams?.phone && { phone: eventParams.phone }),
              ...(eventParams?.firstName && { firstName: eventParams.firstName }),
              ...(eventParams?.lastName && { lastName: eventParams.lastName }),
              // Include Facebook browser parameters if available
              fbp: getCookie('_fbp'),
              fbc: getCookie('_fbc') || getURLParameter('fbclid'),
            },
          }),
        });

        if (!response.ok) {
          throw new Error(`Server responded with ${response.status}`);
        }

        const data = await response.json();

        if (data.warning) {
          console.warn(`[FB Conversion API] Warning: ${data.warning}`);
        } else {
          console.log(`[FB Conversion API] Tracked standard event: ${eventName}`);
        }
      } catch (serverError) {
        console.error('[FB Conversion API] Error tracking standard event:', serverError);
      }
    }

    // Also track in our custom event tracking system
    try {
      await trackCustomEvent(eventName, eventName, eventParams);
    } catch (trackingError) {
      console.error('[Custom Tracking] Error tracking standard event:', trackingError);
    }
  } catch (error) {
    console.error('[FB Pixel] Error tracking standard event:', error);
  }
};

/**
 * Generate a session ID if one doesn't exist
 * @returns The session ID
 */
export function getSessionId(): string {
  if (typeof window === 'undefined') return '';

  // Check if we already have a session ID in localStorage
  let sessionId = localStorage.getItem('mocky_session_id');

  // If not, generate a new one
  if (!sessionId) {
    sessionId = `${Date.now()}-${Math.random().toString(36).substring(2, 15)}`;
    localStorage.setItem('mocky_session_id', sessionId);
  }

  return sessionId;
}

/**
 * Track a custom event in our internal tracking system
 * @param eventName The name of the event
 * @param eventType The type of event
 * @param metadata Additional metadata for the event
 * @returns Promise that resolves when the event is tracked
 */
export const trackCustomEvent = async (
  eventName: string,
  eventType: string,
  metadata?: Record<string, any>
): Promise<void> => {
  try {
    if (typeof window === 'undefined') return;

    // Get session ID
    const sessionId = getSessionId();

    // Prepare event data
    const eventData: EventData = {
      eventName,
      eventType,
      url: window.location.href,
      sessionId,
      metadata,
    };

    // Send to our custom tracking API
    const response = await fetch('/api/tracking/event', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(eventData),
    });

    if (!response.ok) {
      throw new Error(`Server responded with ${response.status}`);
    }

    console.log(`[Custom Tracking] Tracked event: ${eventName}`);
  } catch (error) {
    console.error('[Custom Tracking] Error tracking event:', error);
    // Don't rethrow - we don't want to break the app if tracking fails
  }
};

/**
 * Track a page view
 * @param url The URL of the page (defaults to current URL)
 * @param metadata Additional metadata for the event
 */
export const trackPageView = async (
  url?: string,
  metadata?: Record<string, any>
): Promise<void> => {
  try {
    if (typeof window === 'undefined') return;

    const pageUrl = url || window.location.href;
    const sessionId = getSessionId();

    // Track with our custom tracking system
    await trackCustomEvent('pageView', 'pageView', {
      ...metadata,
      url: pageUrl,
    });

    console.log(`[Custom Tracking] Tracked page view: ${pageUrl}`);
  } catch (error) {
    console.error('[Custom Tracking] Error tracking page view:', error);
  }
};

/**
 * Track a button click
 * @param buttonName The name or ID of the button
 * @param metadata Additional metadata for the event
 */
export const trackButtonClick = async (
  buttonName: string,
  metadata?: Record<string, any>
): Promise<void> => {
  try {
    if (typeof window === 'undefined') return;

    // Track with our custom tracking system
    await trackCustomEvent(buttonName, 'buttonClick', metadata);

    console.log(`[Custom Tracking] Tracked button click: ${buttonName}`);
  } catch (error) {
    console.error('[Custom Tracking] Error tracking button click:', error);
  }
};

/**
 * Track a form submission
 * @param formName The name or ID of the form
 * @param formData The data submitted with the form
 */
export const trackFormSubmission = async (
  formName: string,
  formData?: Record<string, any>
): Promise<void> => {
  try {
    if (typeof window === 'undefined') return;

    // Track with our custom tracking system
    await trackCustomEvent(formName, 'formSubmission', formData);

    console.log(`[Custom Tracking] Tracked form submission: ${formName}`);
  } catch (error) {
    console.error('[Custom Tracking] Error tracking form submission:', error);
  }
};

/**
 * Track a form abandonment
 * @param formName The name or ID of the form
 * @param completionPercentage The percentage of the form that was completed
 * @param formData The partial data entered in the form
 */
export const trackFormAbandonment = async (
  formName: string,
  completionPercentage: number,
  formData?: Record<string, any>
): Promise<void> => {
  try {
    if (typeof window === 'undefined') return;

    // Track with our custom tracking system
    await trackCustomEvent(formName, 'formAbandonment', {
      ...formData,
      completionPercentage,
    });

    console.log(`[Custom Tracking] Tracked form abandonment: ${formName} (${completionPercentage}% complete)`);
  } catch (error) {
    console.error('[Custom Tracking] Error tracking form abandonment:', error);
  }
};
