'use client';

import { useEffect, useState } from 'react';
import {
  DocumentTextIcon,
  FolderIcon,
  TagIcon,
  UserGroupIcon,
  Cog6ToothIcon,
  ChartBarIcon,
  PhotoIcon,
  GlobeAltIcon,
  CurrencyDollarIcon,
  ServerIcon,
  ChevronDownIcon,
  UsersIcon,
  BookOpenIcon,
  XMarkIcon,
  HomeIcon,
  ArrowTopRightOnSquareIcon,
  ReceiptRefundIcon,
  BanknotesIcon,
  ShoppingBagIcon,
  NewspaperIcon,
  SquaresPlusIcon,
  ShieldCheckIcon,
  ClockIcon,
  PresentationChartLineIcon,
  CodeBracketIcon,
  UserIcon,
  ArrowTrendingUpIcon,
  ChartBarSquareIcon,
  MagnifyingGlassIcon,
} from '@heroicons/react/24/outline';
import { CircleStackIcon as DatabaseIcon } from '@heroicons/react/24/outline';
import { usePathname } from 'next/navigation';
import Link from 'next/link';

// Dashboard items
const dashboardItems = [
  {
    id: 'dashboard',
    name: 'Dashboard',
    icon: ChartBarIcon,
    path: '/admin/dashboard',
    highlight: true
  },
  {
    id: 'analytics',
    name: 'Analytics',
    icon: PresentationChartLineIcon,
    path: '/admin/analytics',
    highlight: true
  },
  {
    id: 'tracking',
    name: 'Website Tracking',
    icon: ChartBarSquareIcon,
    path: '/admin/tracking',
    highlight: true
  }
];

// Content Management submenu items
const contentMenuItems = [
  { id: 'blog', name: 'Blog Posts', icon: DocumentTextIcon, path: '/admin/blog', highlight: true },
  { id: 'scheduled-blog-posts', name: 'Scheduled Posts', icon: DocumentTextIcon, path: '/admin/scheduled-blog-posts', highlight: true },
  { id: 'portfolio', name: 'Portfolio', icon: PhotoIcon, path: '/admin/portfolio', highlight: true },
  { id: 'website-portfolio', name: 'Website Portfolio', icon: GlobeAltIcon, path: '/admin/website-portfolio', highlight: true },
  { id: 'team', name: 'Team Members', icon: UsersIcon, path: '/admin/team', highlight: true },
];

// Receipting System submenu items
const receiptingMenuItems = [
  { id: 'transactions', name: 'Transactions', icon: BanknotesIcon, path: '/admin/transactions', highlight: true },
  { id: 'receipts', name: 'Receipts', icon: ReceiptRefundIcon, path: '/admin/receipts', highlight: true },
  { id: 'invoices', name: 'Invoices', icon: DocumentTextIcon, path: '/admin/invoices', highlight: true },
  { id: 'quotes', name: 'Quotes', icon: NewspaperIcon, path: '/admin/quotes', highlight: true },
  { id: 'services', name: 'Services', icon: ShoppingBagIcon, path: '/admin/services', highlight: true },
];

// Website Configuration submenu items
const configMenuItems = [
  { id: 'pricing', name: 'Pricing', icon: CurrencyDollarIcon, path: '/admin/pricing', highlight: true },
  { id: 'database', name: 'Database Management', icon: DatabaseIcon, path: '/admin/database', highlight: true },
  { id: 'categories', name: 'Categories', icon: FolderIcon, path: '/admin/categories', highlight: true },
  { id: 'tags', name: 'Tags', icon: TagIcon, path: '/admin/tags', highlight: true },
  { id: 'authors', name: 'Authors', icon: UserGroupIcon, path: '/admin/authors', highlight: true },
  { id: 'seo', name: 'SEO Management', icon: MagnifyingGlassIcon, path: '/admin/seo', highlight: true },
];

// User Management submenu items
const userManagementMenuItems = [
  { id: 'users', name: 'Users', icon: UserGroupIcon, path: '/admin/users', highlight: true },
  { id: 'roles', name: 'Roles & Permissions', icon: ShieldCheckIcon, path: '/admin/roles', highlight: true },
  { id: 'activity-logs', name: 'Activity Logs', icon: ClockIcon, path: '/admin/activity-logs', highlight: true },
];

// Lead Management submenu items
const leadManagementMenuItems = [
  { id: 'leads', name: 'Lead Management', icon: UserIcon, path: '/admin/leads', highlight: true },
  { id: 'lead-pipeline', name: 'Lead Pipeline', icon: ArrowTrendingUpIcon, path: '/admin/leads/pipeline', highlight: true },
];

// Settings submenu items
const settingsMenuItems = [
  { id: 'general-settings', name: 'General Settings', icon: Cog6ToothIcon, path: '/admin/settings', highlight: true },
  { id: 'storage-settings', name: 'Storage Settings', icon: ServerIcon, path: '/admin/settings/storage', highlight: true },
  { id: 'scripts-settings', name: 'Site Scripts', icon: CodeBracketIcon, path: '/admin/settings/scripts', highlight: true },
];

interface AdminSidebarProps {
  open: boolean;
  setOpen: (open: boolean) => void;
}

export default function AdminSidebar({ open, setOpen }: AdminSidebarProps) {
  const [mounted, setMounted] = useState(false);
  const [settingsOpen, setSettingsOpen] = useState(false);
  const [contentOpen, setContentOpen] = useState(false);
  const [receiptingOpen, setReceiptingOpen] = useState(false);
  const [configOpen, setConfigOpen] = useState(false);
  const [userManagementOpen, setUserManagementOpen] = useState(false);
  const [leadManagementOpen, setLeadManagementOpen] = useState(false);
  const pathname = usePathname();

  useEffect(() => {
    setMounted(true);

    // Auto-expand sections based on current path
    if (pathname) {
      if (pathname.startsWith('/admin/settings')) {
        setSettingsOpen(true);
      }

      if (pathname.startsWith('/admin/blog') ||
          pathname.startsWith('/admin/portfolio') ||
          pathname.startsWith('/admin/website-portfolio') ||
          pathname.startsWith('/admin/team') ||
          pathname.startsWith('/admin/scheduled-blog-posts')) {
        setContentOpen(true);
      }

      if (pathname.startsWith('/admin/transactions') ||
          pathname.startsWith('/admin/receipts') ||
          pathname.startsWith('/admin/services') ||
          pathname.startsWith('/admin/invoices') ||
          pathname.startsWith('/admin/quotes')) {
        setReceiptingOpen(true);
      }

      if (pathname.startsWith('/admin/pricing') ||
          pathname.startsWith('/admin/database') ||
          pathname.startsWith('/admin/categories') ||
          pathname.startsWith('/admin/tags') ||
          pathname.startsWith('/admin/authors') ||
          pathname.startsWith('/admin/seo')) {
        setConfigOpen(true);
      }

      if (pathname.startsWith('/admin/users') ||
          pathname.startsWith('/admin/roles') ||
          pathname.startsWith('/admin/activity-logs')) {
        setUserManagementOpen(true);
      }

      if (pathname.startsWith('/admin/leads')) {
        setLeadManagementOpen(true);
      }
    }
  }, [pathname]);

  if (!mounted) {
    return null;
  }

  return (
    <>
      {/* Desktop sidebar - always visible on md and up */}
      <div className="hidden md:block fixed left-0 top-20 w-60 bg-white h-[calc(100vh-5rem)] border-r border-[#0A1929]/10 z-10 pt-10 overflow-y-auto shadow-sm">
        <SidebarContent
          pathname={pathname}
          settingsOpen={settingsOpen}
          setSettingsOpen={setSettingsOpen}
          contentOpen={contentOpen}
          setContentOpen={setContentOpen}
          receiptingOpen={receiptingOpen}
          setReceiptingOpen={setReceiptingOpen}
          configOpen={configOpen}
          setConfigOpen={setConfigOpen}
          userManagementOpen={userManagementOpen}
          setUserManagementOpen={setUserManagementOpen}
          leadManagementOpen={leadManagementOpen}
          setLeadManagementOpen={setLeadManagementOpen}
        />
      </div>

      {/* Mobile sidebar - slides in from left */}
      <div
        className={`md:hidden fixed inset-y-0 left-0 z-30 w-full max-w-[280px] bg-white shadow-lg transform transition-transform duration-300 ease-in-out ${
          open ? 'translate-x-0' : '-translate-x-full'
        }`}
      >
        <div className="flex items-center justify-between p-4 border-b border-[#0A1929]/10 bg-white">
          <h2 className="text-lg font-medium text-[#0A1929]">Menu</h2>
          <button
            onClick={() => setOpen(false)}
            className="p-2 rounded-md text-[#0A1929] hover:text-[#FF5400] focus:outline-none"
          >
            <XMarkIcon className="h-6 w-6" />
          </button>
        </div>
        <div className="p-4 pt-10 h-[calc(100vh-64px)] overflow-y-auto">
          <SidebarContent
            pathname={pathname}
            settingsOpen={settingsOpen}
            setSettingsOpen={setSettingsOpen}
            contentOpen={contentOpen}
            setContentOpen={setContentOpen}
            receiptingOpen={receiptingOpen}
            setReceiptingOpen={setReceiptingOpen}
            configOpen={configOpen}
            setConfigOpen={setConfigOpen}
            userManagementOpen={userManagementOpen}
            setUserManagementOpen={setUserManagementOpen}
            leadManagementOpen={leadManagementOpen}
            setLeadManagementOpen={setLeadManagementOpen}
            onLinkClick={() => setOpen(false)}
          />
        </div>
      </div>
    </>
  );
}

// Extracted sidebar content to avoid duplication
function SidebarContent({
  pathname,
  settingsOpen,
  setSettingsOpen,
  contentOpen,
  setContentOpen,
  receiptingOpen,
  setReceiptingOpen,
  configOpen,
  setConfigOpen,
  userManagementOpen,
  setUserManagementOpen,
  leadManagementOpen,
  setLeadManagementOpen,
  onLinkClick
}: {
  pathname: string | null;
  settingsOpen: boolean;
  setSettingsOpen: (open: boolean) => void;
  contentOpen?: boolean;
  setContentOpen?: (open: boolean) => void;
  receiptingOpen?: boolean;
  setReceiptingOpen?: (open: boolean) => void;
  configOpen?: boolean;
  setConfigOpen?: (open: boolean) => void;
  userManagementOpen?: boolean;
  setUserManagementOpen?: (open: boolean) => void;
  leadManagementOpen?: boolean;
  setLeadManagementOpen?: (open: boolean) => void;
  onLinkClick?: () => void;
}) {
  // Helper function to render a menu item
  const renderMenuItem = (item: any) => {
    const isActive = pathname === item.path;
    return (
      <Link
        key={item.id}
        href={item.path}
        className={`flex items-center px-4 py-4 my-1 text-sm font-medium rounded-lg transition-all ${
          isActive
            ? 'bg-[#0A1929]/10 text-[#0A1929] border-l-4 border-[#0A1929]'
            : 'text-[#0A1929] hover:bg-[#FF5400]/10 hover:text-[#FF5400] bg-white'
        }`}
        onClick={onLinkClick}
      >
        <item.icon className={`mr-3 h-5 w-5 ${!isActive ? 'text-[#FF5400]' : ''}`} />
        {item.name}
      </Link>
    );
  };

  // Helper function to render a submenu
  const renderSubmenu = (items: any[], isOpen: boolean) => {
    return (
      <div className={`overflow-hidden transition-all duration-300 ease-in-out ${
        isOpen ? 'max-h-96 opacity-100' : 'max-h-0 opacity-0'
      }`}>
        <div className="ml-4 pl-2 border-l-2 border-[#FF5400]/20 space-y-2 py-2 mt-2">
          {items.map((item) => {
            const isActive = pathname === item.path;
            return (
              <Link
                key={item.id}
                href={item.path}
                className={`flex items-center px-4 py-3 my-1 text-sm font-medium rounded-lg transition-all ${
                  isActive
                    ? 'bg-[#0A1929]/10 text-[#0A1929] border-l-4 border-[#0A1929]'
                    : 'text-[#0A1929] bg-white hover:bg-[#FF5400]/10 hover:text-[#FF5400]'
                }`}
                onClick={onLinkClick}
              >
                <item.icon className="mr-3 h-5 w-5 text-[#FF5400]" />
                {item.name}
              </Link>
            );
          })}
        </div>
      </div>
    );
  };

  return (
    <nav className="space-y-4 mt-6">
      {/* Dashboard items */}
      {dashboardItems.map(item => renderMenuItem(item))}

      {/* Content Management section with collapsible submenu */}
      <div className="mt-8 pt-4 border-t border-[#0A1929]/10">
        <button
          onClick={() => setContentOpen && setContentOpen(!contentOpen)}
          className={`flex items-center justify-between w-full px-4 py-4 my-1 text-sm font-medium rounded-lg transition-all text-[#0A1929] bg-white hover:bg-[#FF5400]/10 hover:text-[#FF5400]`}
        >
          <div className="flex items-center">
            <SquaresPlusIcon className="mr-3 h-5 w-5 text-[#FF5400]" />
            <span>Content Management</span>
          </div>
          <ChevronDownIcon className={`h-4 w-4 transition-transform ${contentOpen ? 'rotate-180' : ''}`} />
        </button>
        {renderSubmenu(contentMenuItems, contentOpen || false)}
      </div>

      {/* Receipting System section with collapsible submenu */}
      <div className="mt-8 pt-4 border-t border-[#0A1929]/10">
        <button
          onClick={() => setReceiptingOpen && setReceiptingOpen(!receiptingOpen)}
          className={`flex items-center justify-between w-full px-4 py-4 my-1 text-sm font-medium rounded-lg transition-all text-[#0A1929] bg-white hover:bg-[#FF5400]/10 hover:text-[#FF5400]`}
        >
          <div className="flex items-center">
            <ReceiptRefundIcon className="mr-3 h-5 w-5 text-[#FF5400]" />
            <span>Receipting System</span>
          </div>
          <ChevronDownIcon className={`h-4 w-4 transition-transform ${receiptingOpen ? 'rotate-180' : ''}`} />
        </button>
        {renderSubmenu(receiptingMenuItems, receiptingOpen || false)}
      </div>

      {/* Website Configuration section with collapsible submenu */}
      <div className="mt-8 pt-4 border-t border-[#0A1929]/10">
        <button
          onClick={() => setConfigOpen && setConfigOpen(!configOpen)}
          className={`flex items-center justify-between w-full px-4 py-4 my-1 text-sm font-medium rounded-lg transition-all text-[#0A1929] bg-white hover:bg-[#FF5400]/10 hover:text-[#FF5400]`}
        >
          <div className="flex items-center">
            <GlobeAltIcon className="mr-3 h-5 w-5 text-[#FF5400]" />
            <span>Website Configuration</span>
          </div>
          <ChevronDownIcon className={`h-4 w-4 transition-transform ${configOpen ? 'rotate-180' : ''}`} />
        </button>
        {renderSubmenu(configMenuItems, configOpen || false)}
      </div>

      {/* User Management section with collapsible submenu */}
      <div className="mt-8 pt-4 border-t border-[#0A1929]/10">
        <button
          onClick={() => setUserManagementOpen && setUserManagementOpen(!userManagementOpen)}
          className={`flex items-center justify-between w-full px-4 py-4 my-1 text-sm font-medium rounded-lg transition-all text-[#0A1929] bg-white hover:bg-[#FF5400]/10 hover:text-[#FF5400]`}
        >
          <div className="flex items-center">
            <UserGroupIcon className="mr-3 h-5 w-5 text-[#FF5400]" />
            <span>User Management</span>
          </div>
          <ChevronDownIcon className={`h-4 w-4 transition-transform ${userManagementOpen ? 'rotate-180' : ''}`} />
        </button>
        {renderSubmenu(userManagementMenuItems, userManagementOpen || false)}
      </div>

      {/* Lead Management section with collapsible submenu */}
      <div className="mt-8 pt-4 border-t border-[#0A1929]/10">
        <button
          onClick={() => setLeadManagementOpen && setLeadManagementOpen(!leadManagementOpen)}
          className={`flex items-center justify-between w-full px-4 py-4 my-1 text-sm font-medium rounded-lg transition-all text-[#0A1929] bg-white hover:bg-[#FF5400]/10 hover:text-[#FF5400]`}
        >
          <div className="flex items-center">
            <UserIcon className="mr-3 h-5 w-5 text-[#FF5400]" />
            <span>Lead Management</span>
          </div>
          <ChevronDownIcon className={`h-4 w-4 transition-transform ${leadManagementOpen ? 'rotate-180' : ''}`} />
        </button>
        {renderSubmenu(leadManagementMenuItems, leadManagementOpen || false)}
      </div>

      {/* Settings section with collapsible submenu */}
      <div className="mt-8 pt-4 border-t border-[#0A1929]/10">
        <button
          onClick={() => setSettingsOpen(!settingsOpen)}
          className={`flex items-center justify-between w-full px-4 py-4 my-1 text-sm font-medium rounded-lg transition-all text-[#0A1929] bg-white hover:bg-[#FF5400]/10 hover:text-[#FF5400]`}
        >
          <div className="flex items-center">
            <Cog6ToothIcon className="mr-3 h-5 w-5 text-[#FF5400]" />
            <span>Settings</span>
          </div>
          <ChevronDownIcon className={`h-4 w-4 transition-transform ${settingsOpen ? 'rotate-180' : ''}`} />
        </button>
        {renderSubmenu(settingsMenuItems, settingsOpen)}
      </div>

      {/* Main Site Link */}
      <div className="mt-8 pt-6 border-t border-[#0A1929]/10">
        <Link
          href="/"
          className="flex items-center justify-between px-4 py-4 my-1 text-sm font-medium rounded-lg transition-all text-[#0A1929] bg-white hover:bg-[#FF5400]/10 hover:text-[#FF5400]"
          target="_blank"
          rel="noopener noreferrer"
          onClick={onLinkClick}
        >
          <div className="flex items-center">
            <HomeIcon className="mr-3 h-5 w-5 text-[#FF5400]" />
            <span>Visit Main Site</span>
          </div>
          <ArrowTopRightOnSquareIcon className="h-4 w-4" />
        </Link>
      </div>
    </nav>
  );
}