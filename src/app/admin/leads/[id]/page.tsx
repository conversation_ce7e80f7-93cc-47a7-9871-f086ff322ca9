'use client';

import { useState, useEffect } from 'react';
import { useParams, useRouter } from 'next/navigation';
import { useSession } from 'next-auth/react';
import {
  UserIcon,
  PhoneIcon,
  EnvelopeIcon,
  BuildingOfficeIcon,
  ArrowLeftIcon,
  PencilIcon,
  CalendarIcon,
  ChatBubbleLeftRightIcon,
  ChartBarIcon,
} from '@heroicons/react/24/outline';
import { useNotification } from '@/contexts/NotificationContext';
import Link from 'next/link';

interface Lead {
  id: string;
  name: string;
  email: string;
  phone?: string;
  company?: string;
  source?: string;
  status: string;
  score: number;
  notes?: string;
  assignedToUserId?: string;
  lastContactedAt?: string;
  nextFollowUpDate?: string;
  createdAt: string;
  updatedAt: string;
  assignedTo?: {
    id: string;
    name: string;
    username: string;
  };
  interactions: Interaction[];
}

interface Interaction {
  id: string;
  leadId: string;
  type: string;
  details?: string;
  createdAt: string;
  createdBy?: string;
}

interface User {
  id: string;
  name: string;
  username: string;
}

export default function LeadDetailPage() {
  const params = useParams();
  const router = useRouter();
  const { data: session } = useSession();
  const { showNotification } = useNotification();
  const [lead, setLead] = useState<Lead | null>(null);
  const [users, setUsers] = useState<User[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isEditing, setIsEditing] = useState(false);
  const [formData, setFormData] = useState({
    status: '',
    assignedToUserId: '',
    nextFollowUpDate: '',
    notes: '',
  });
  const [newInteraction, setNewInteraction] = useState({
    type: 'note',
    details: '',
  });

  // Fetch lead data on component mount
  useEffect(() => {
    if (params.id) {
      fetchLead(params.id as string);
      fetchUsers();
    }
  }, [params.id]);

  // Fetch lead data
  const fetchLead = async (id: string) => {
    try {
      setIsLoading(true);
      setError(null);

      const response = await fetch(`/api/admin/leads/${id}`);
      
      if (!response.ok) {
        throw new Error(`Error fetching lead: ${response.status}`);
      }

      const data = await response.json();
      setLead(data);
      setFormData({
        status: data.status,
        assignedToUserId: data.assignedToUserId || '',
        nextFollowUpDate: data.nextFollowUpDate ? new Date(data.nextFollowUpDate).toISOString().split('T')[0] : '',
        notes: data.notes || '',
      });
    } catch (err) {
      setError('Failed to load lead details. Please try again.');
      console.error('Error fetching lead:', err);
      showNotification('error', 'Failed to load lead details');
    } finally {
      setIsLoading(false);
    }
  };

  // Fetch users for assignment
  const fetchUsers = async () => {
    try {
      const response = await fetch('/api/admin/users');
      
      if (!response.ok) {
        throw new Error(`Error fetching users: ${response.status}`);
      }

      const data = await response.json();
      setUsers(data);
    } catch (err) {
      console.error('Error fetching users:', err);
    }
  };

  // Handle form input changes
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value,
    }));
  };

  // Handle new interaction input changes
  const handleInteractionChange = (e: React.ChangeEvent<HTMLSelectElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setNewInteraction(prev => ({
      ...prev,
      [name]: value,
    }));
  };

  // Save lead changes
  const saveLead = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!lead) return;

    try {
      const response = await fetch(`/api/admin/leads/${lead.id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData),
      });

      if (!response.ok) {
        throw new Error(`Error updating lead: ${response.status}`);
      }

      const updatedLead = await response.json();
      setLead(updatedLead);
      setIsEditing(false);
      showNotification('success', 'Lead updated successfully');
    } catch (err) {
      console.error('Error updating lead:', err);
      showNotification('error', 'Failed to update lead');
    }
  };

  // Add new interaction
  const addInteraction = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!lead) return;
    if (!newInteraction.details.trim()) {
      showNotification('error', 'Interaction details cannot be empty');
      return;
    }

    try {
      const response = await fetch(`/api/admin/leads/${lead.id}/interactions`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(newInteraction),
      });

      if (!response.ok) {
        throw new Error(`Error adding interaction: ${response.status}`);
      }

      // Refresh lead data to get the new interaction
      fetchLead(lead.id);
      setNewInteraction({
        type: 'note',
        details: '',
      });
      showNotification('success', 'Interaction added successfully');
    } catch (err) {
      console.error('Error adding interaction:', err);
      showNotification('error', 'Failed to add interaction');
    }
  };

  // Calculate lead score
  const recalculateScore = async () => {
    if (!lead) return;

    try {
      const response = await fetch(`/api/admin/leads/${lead.id}/score`, {
        method: 'POST',
      });

      if (!response.ok) {
        throw new Error(`Error recalculating score: ${response.status}`);
      }

      const data = await response.json();
      setLead(prev => prev ? { ...prev, score: data.score } : null);
      showNotification('success', `Lead score updated to ${data.score}`);
    } catch (err) {
      console.error('Error recalculating score:', err);
      showNotification('error', 'Failed to recalculate lead score');
    }
  };

  // Get status badge color
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'new':
        return 'bg-blue-100 text-blue-800';
      case 'contacted':
        return 'bg-yellow-100 text-yellow-800';
      case 'qualified':
        return 'bg-green-100 text-green-800';
      case 'proposal':
        return 'bg-purple-100 text-purple-800';
      case 'converted':
        return 'bg-emerald-100 text-emerald-800';
      case 'closed':
        return 'bg-gray-100 text-gray-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  // Get score color
  const getScoreColor = (score: number) => {
    if (score >= 80) return 'text-emerald-600';
    if (score >= 60) return 'text-green-600';
    if (score >= 40) return 'text-yellow-600';
    if (score >= 20) return 'text-orange-600';
    return 'text-gray-600';
  };

  // Format date
  const formatDate = (dateString?: string) => {
    if (!dateString) return 'N/A';
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  // Get interaction icon
  const getInteractionIcon = (type: string) => {
    switch (type) {
      case 'note':
        return <ChatBubbleLeftRightIcon className="h-5 w-5 text-gray-500" />;
      case 'call':
        return <PhoneIcon className="h-5 w-5 text-blue-500" />;
      case 'email':
        return <EnvelopeIcon className="h-5 w-5 text-green-500" />;
      case 'meeting':
        return <CalendarIcon className="h-5 w-5 text-purple-500" />;
      case 'form_submission':
        return <PencilIcon className="h-5 w-5 text-orange-500" />;
      default:
        return <ChatBubbleLeftRightIcon className="h-5 w-5 text-gray-500" />;
    }
  };

  if (isLoading) {
    return (
      <div className="p-8 text-center">
        <div className="inline-block animate-spin rounded-full h-8 w-8 border-4 border-indigo-500 border-t-transparent"></div>
        <p className="mt-2 text-gray-600">Loading lead details...</p>
      </div>
    );
  }

  if (error || !lead) {
    return (
      <div className="p-8 text-center text-red-500">
        {error || 'Lead not found'}
        <div className="mt-4">
          <Link
            href="/admin/leads"
            className="inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
          >
            <ArrowLeftIcon className="-ml-1 mr-2 h-5 w-5" />
            Back to Leads
          </Link>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div className="flex items-center">
          <Link
            href="/admin/leads"
            className="mr-4 p-2 text-gray-500 hover:text-gray-700 hover:bg-gray-100 rounded-full"
          >
            <ArrowLeftIcon className="h-5 w-5" />
          </Link>
          <div className="flex-shrink-0 h-12 w-12 bg-indigo-100 rounded-full flex items-center justify-center">
            <UserIcon className="h-8 w-8 text-indigo-600" />
          </div>
          <div className="ml-4">
            <h1 className="text-2xl font-semibold text-slate-800">{lead.name}</h1>
            <div className="flex items-center space-x-3 text-sm text-gray-500">
              <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${getStatusColor(lead.status)}`}>
                {lead.status.charAt(0).toUpperCase() + lead.status.slice(1)}
              </span>
              <span className={`font-medium ${getScoreColor(lead.score)}`}>
                Score: {lead.score}/100
              </span>
              <span>Created: {formatDate(lead.createdAt)}</span>
            </div>
          </div>
        </div>

        <div className="flex space-x-2">
          <button
            onClick={() => setIsEditing(!isEditing)}
            className="inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
          >
            <PencilIcon className="-ml-1 mr-2 h-5 w-5" />
            {isEditing ? 'Cancel' : 'Edit Lead'}
          </button>
          <button
            onClick={recalculateScore}
            className="inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
          >
            <ChartBarIcon className="-ml-1 mr-2 h-5 w-5" />
            Recalculate Score
          </button>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Lead Details */}
        <div className="lg:col-span-2 space-y-6">
          {/* Contact Information */}
          <div className="bg-white shadow rounded-lg p-6">
            <h2 className="text-lg font-medium text-gray-900 mb-4">Contact Information</h2>
            {isEditing ? (
              <form onSubmit={saveLead} className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label htmlFor="status" className="block text-sm font-medium text-gray-700 mb-1">
                      Status
                    </label>
                    <select
                      id="status"
                      name="status"
                      value={formData.status}
                      onChange={handleInputChange}
                      className="block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm rounded-md"
                    >
                      <option value="new">New</option>
                      <option value="contacted">Contacted</option>
                      <option value="qualified">Qualified</option>
                      <option value="proposal">Proposal</option>
                      <option value="converted">Converted</option>
                      <option value="closed">Closed</option>
                    </select>
                  </div>
                  <div>
                    <label htmlFor="assignedToUserId" className="block text-sm font-medium text-gray-700 mb-1">
                      Assigned To
                    </label>
                    <select
                      id="assignedToUserId"
                      name="assignedToUserId"
                      value={formData.assignedToUserId}
                      onChange={handleInputChange}
                      className="block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm rounded-md"
                    >
                      <option value="">Unassigned</option>
                      {users.map(user => (
                        <option key={user.id} value={user.id}>{user.name}</option>
                      ))}
                    </select>
                  </div>
                </div>
                <div>
                  <label htmlFor="nextFollowUpDate" className="block text-sm font-medium text-gray-700 mb-1">
                    Next Follow-up Date
                  </label>
                  <input
                    type="date"
                    id="nextFollowUpDate"
                    name="nextFollowUpDate"
                    value={formData.nextFollowUpDate}
                    onChange={handleInputChange}
                    className="block w-full pl-3 pr-3 py-2 text-base border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm rounded-md"
                  />
                </div>
                <div>
                  <label htmlFor="notes" className="block text-sm font-medium text-gray-700 mb-1">
                    Notes
                  </label>
                  <textarea
                    id="notes"
                    name="notes"
                    rows={4}
                    value={formData.notes}
                    onChange={handleInputChange}
                    className="block w-full pl-3 pr-3 py-2 text-base border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm rounded-md"
                  ></textarea>
                </div>
                <div className="flex justify-end">
                  <button
                    type="submit"
                    className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                  >
                    Save Changes
                  </button>
                </div>
              </form>
            ) : (
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <p className="text-sm font-medium text-gray-500">Email</p>
                  <p className="mt-1 flex items-center">
                    <EnvelopeIcon className="h-4 w-4 mr-1 text-gray-400" />
                    <a href={`mailto:${lead.email}`} className="text-indigo-600 hover:text-indigo-800">
                      {lead.email}
                    </a>
                  </p>
                </div>
                {lead.phone && (
                  <div>
                    <p className="text-sm font-medium text-gray-500">Phone</p>
                    <p className="mt-1 flex items-center">
                      <PhoneIcon className="h-4 w-4 mr-1 text-gray-400" />
                      <a href={`tel:${lead.phone}`} className="text-indigo-600 hover:text-indigo-800">
                        {lead.phone}
                      </a>
                    </p>
                  </div>
                )}
                {lead.company && (
                  <div>
                    <p className="text-sm font-medium text-gray-500">Company</p>
                    <p className="mt-1 flex items-center">
                      <BuildingOfficeIcon className="h-4 w-4 mr-1 text-gray-400" />
                      {lead.company}
                    </p>
                  </div>
                )}
                <div>
                  <p className="text-sm font-medium text-gray-500">Source</p>
                  <p className="mt-1">{lead.source || 'Website'}</p>
                </div>
                <div>
                  <p className="text-sm font-medium text-gray-500">Assigned To</p>
                  <p className="mt-1">{lead.assignedTo ? lead.assignedTo.name : 'Unassigned'}</p>
                </div>
                <div>
                  <p className="text-sm font-medium text-gray-500">Next Follow-up</p>
                  <p className="mt-1">{lead.nextFollowUpDate ? formatDate(lead.nextFollowUpDate) : 'Not scheduled'}</p>
                </div>
                {lead.notes && (
                  <div className="md:col-span-2">
                    <p className="text-sm font-medium text-gray-500">Notes</p>
                    <p className="mt-1 whitespace-pre-line">{lead.notes}</p>
                  </div>
                )}
              </div>
            )}
          </div>

          {/* Interactions */}
          <div className="bg-white shadow rounded-lg p-6">
            <h2 className="text-lg font-medium text-gray-900 mb-4">Interactions</h2>
            
            {/* Add Interaction Form */}
            <form onSubmit={addInteraction} className="mb-6 p-4 bg-gray-50 rounded-lg">
              <h3 className="text-md font-medium text-gray-700 mb-3">Add New Interaction</h3>
              <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                <div className="md:col-span-1">
                  <label htmlFor="type" className="block text-sm font-medium text-gray-700 mb-1">
                    Type
                  </label>
                  <select
                    id="type"
                    name="type"
                    value={newInteraction.type}
                    onChange={handleInteractionChange}
                    className="block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm rounded-md"
                  >
                    <option value="note">Note</option>
                    <option value="call">Call</option>
                    <option value="email">Email</option>
                    <option value="meeting">Meeting</option>
                  </select>
                </div>
                <div className="md:col-span-3">
                  <label htmlFor="details" className="block text-sm font-medium text-gray-700 mb-1">
                    Details
                  </label>
                  <textarea
                    id="details"
                    name="details"
                    rows={2}
                    value={newInteraction.details}
                    onChange={handleInteractionChange}
                    className="block w-full pl-3 pr-3 py-2 text-base border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm rounded-md"
                    placeholder="Enter interaction details..."
                  ></textarea>
                </div>
              </div>
              <div className="mt-3 flex justify-end">
                <button
                  type="submit"
                  className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                >
                  Add Interaction
                </button>
              </div>
            </form>
            
            {/* Interactions List */}
            {lead.interactions.length === 0 ? (
              <p className="text-center text-gray-500 py-4">No interactions recorded yet.</p>
            ) : (
              <div className="flow-root">
                <ul className="-mb-8">
                  {lead.interactions.map((interaction, idx) => (
                    <li key={interaction.id}>
                      <div className="relative pb-8">
                        {idx !== lead.interactions.length - 1 && (
                          <span className="absolute top-5 left-5 -ml-px h-full w-0.5 bg-gray-200" aria-hidden="true"></span>
                        )}
                        <div className="relative flex items-start space-x-3">
                          <div className="relative">
                            <div className="h-10 w-10 rounded-full bg-gray-100 flex items-center justify-center ring-8 ring-white">
                              {getInteractionIcon(interaction.type)}
                            </div>
                          </div>
                          <div className="min-w-0 flex-1">
                            <div>
                              <div className="text-sm">
                                <span className="font-medium text-gray-900">
                                  {interaction.type.charAt(0).toUpperCase() + interaction.type.slice(1).replace('_', ' ')}
                                </span>
                              </div>
                              <p className="mt-0.5 text-sm text-gray-500">
                                {formatDate(interaction.createdAt)}
                              </p>
                            </div>
                            {interaction.details && (
                              <div className="mt-2 text-sm text-gray-700">
                                <p>{interaction.details}</p>
                              </div>
                            )}
                          </div>
                        </div>
                      </div>
                    </li>
                  ))}
                </ul>
              </div>
            )}
          </div>
        </div>

        {/* Sidebar */}
        <div className="space-y-6">
          {/* Activity Timeline */}
          <div className="bg-white shadow rounded-lg p-6">
            <h2 className="text-lg font-medium text-gray-900 mb-4">Activity Timeline</h2>
            <div className="flow-root">
              <ul className="-mb-8">
                <li>
                  <div className="relative pb-8">
                    <span className="absolute top-5 left-5 -ml-px h-full w-0.5 bg-gray-200" aria-hidden="true"></span>
                    <div className="relative flex items-start space-x-3">
                      <div className="relative">
                        <div className="h-10 w-10 rounded-full bg-blue-100 flex items-center justify-center ring-8 ring-white">
                          <UserIcon className="h-5 w-5 text-blue-600" />
                        </div>
                      </div>
                      <div className="min-w-0 flex-1">
                        <div>
                          <div className="text-sm">
                            <span className="font-medium text-gray-900">Lead Created</span>
                          </div>
                          <p className="mt-0.5 text-sm text-gray-500">
                            {formatDate(lead.createdAt)}
                          </p>
                        </div>
                      </div>
                    </div>
                  </div>
                </li>
                {lead.lastContactedAt && (
                  <li>
                    <div className="relative pb-8">
                      <div className="relative flex items-start space-x-3">
                        <div className="relative">
                          <div className="h-10 w-10 rounded-full bg-green-100 flex items-center justify-center ring-8 ring-white">
                            <ChatBubbleLeftRightIcon className="h-5 w-5 text-green-600" />
                          </div>
                        </div>
                        <div className="min-w-0 flex-1">
                          <div>
                            <div className="text-sm">
                              <span className="font-medium text-gray-900">Last Contact</span>
                            </div>
                            <p className="mt-0.5 text-sm text-gray-500">
                              {formatDate(lead.lastContactedAt)}
                            </p>
                          </div>
                        </div>
                      </div>
                    </div>
                  </li>
                )}
              </ul>
            </div>
          </div>

          {/* Quick Actions */}
          <div className="bg-white shadow rounded-lg p-6">
            <h2 className="text-lg font-medium text-gray-900 mb-4">Quick Actions</h2>
            <div className="space-y-3">
              <a
                href={`mailto:${lead.email}`}
                className="block w-full text-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
              >
                Send Email
              </a>
              {lead.phone && (
                <a
                  href={`tel:${lead.phone}`}
                  className="block w-full text-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                >
                  Call Lead
                </a>
              )}
              <Link
                href={`/admin/leads/${lead.id}/edit`}
                className="block w-full text-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
              >
                Edit Lead
              </Link>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
