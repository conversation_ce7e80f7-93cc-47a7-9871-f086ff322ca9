import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { getLeads, createLead } from '@/services/leadManagementService';

/**
 * GET handler for leads API
 * Returns leads with optional filtering
 */
export async function GET(request: NextRequest) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get query parameters
    const searchParams = request.nextUrl.searchParams;
    const status = searchParams.get('status') || undefined;
    const assignedToUserId = searchParams.get('assignedToUserId') || undefined;
    const minScore = searchParams.get('minScore') ? parseInt(searchParams.get('minScore') as string) : undefined;
    const search = searchParams.get('search') || undefined;

    // Get leads with filters
    const leads = await getLeads({
      status,
      assignedToUserId,
      minScore,
      search,
    });

    // Return the leads
    return NextResponse.json(leads);
  } catch (error) {
    console.error('Error fetching leads:', error);
    return NextResponse.json(
      { error: 'Failed to fetch leads' },
      { status: 500 }
    );
  }
}

/**
 * POST handler for leads API
 * Creates a new lead
 */
export async function POST(request: NextRequest) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get request body
    const body = await request.json();

    // Validate required fields
    if (!body.name || !body.email) {
      return NextResponse.json(
        { error: 'Name and email are required' },
        { status: 400 }
      );
    }

    // Create lead
    const lead = await createLead({
      name: body.name,
      email: body.email,
      phone: body.phone,
      company: body.company,
      source: body.source || 'manual',
      status: body.status || 'new',
      notes: body.notes,
      assignedToUserId: body.assignedToUserId,
      nextFollowUpDate: body.nextFollowUpDate ? new Date(body.nextFollowUpDate) : undefined,
    });

    // Return the created lead
    return NextResponse.json(lead, { status: 201 });
  } catch (error) {
    console.error('Error creating lead:', error);
    return NextResponse.json(
      { error: 'Failed to create lead' },
      { status: 500 }
    );
  }
}
