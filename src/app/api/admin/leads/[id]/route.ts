import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { getLeadById, updateLead } from '@/services/leadManagementService';

interface Params {
  params: {
    id: string;
  };
}

/**
 * GET handler for individual lead API
 * Returns a specific lead by ID
 */
export async function GET(request: NextRequest, { params }: Params) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get lead by ID
    const lead = await getLeadById(params.id);

    if (!lead) {
      return NextResponse.json({ error: 'Lead not found' }, { status: 404 });
    }

    // Return the lead
    return NextResponse.json(lead);
  } catch (error) {
    console.error(`Error fetching lead ${params.id}:`, error);
    return NextResponse.json(
      { error: 'Failed to fetch lead' },
      { status: 500 }
    );
  }
}

/**
 * PUT handler for individual lead API
 * Updates a specific lead by ID
 */
export async function PUT(request: NextRequest, { params }: Params) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get request body
    const body = await request.json();

    // Update lead
    const lead = await updateLead(params.id, {
      status: body.status,
      assignedToUserId: body.assignedToUserId || null,
      nextFollowUpDate: body.nextFollowUpDate ? new Date(body.nextFollowUpDate) : null,
      notes: body.notes,
    });

    // Return the updated lead
    return NextResponse.json(lead);
  } catch (error) {
    console.error(`Error updating lead ${params.id}:`, error);
    return NextResponse.json(
      { error: 'Failed to update lead' },
      { status: 500 }
    );
  }
}
