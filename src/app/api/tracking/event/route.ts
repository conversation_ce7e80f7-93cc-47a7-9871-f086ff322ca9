import { NextRequest, NextResponse } from 'next/server';
import { trackEvent } from '@/services/eventTrackingService';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';

/**
 * POST handler for tracking events
 * This endpoint allows tracking custom events from the client-side
 */
export async function POST(request: NextRequest) {
  try {
    // Parse the request body
    const body = await request.json();
    
    // Validate the request body
    if (!body.eventName || !body.eventType) {
      return NextResponse.json(
        { error: 'Missing required fields: eventName and eventType' },
        { status: 400 }
      );
    }
    
    // Get client IP and user agent
    const clientIp = request.headers.get('x-forwarded-for') || 
                     request.headers.get('x-real-ip') || 
                     'unknown';
    const userAgent = request.headers.get('user-agent') || 'unknown';
    
    // Get the current session if available
    const session = await getServerSession(authOptions);
    const userId = session?.user?.id;
    
    // Prepare the event data
    const eventData = {
      eventName: body.eventName,
      eventType: body.eventType,
      url: body.url || request.headers.get('referer') || undefined,
      userAgent,
      ipAddress: clientIp,
      sessionId: body.sessionId,
      userId,
      leadId: body.leadId,
      metadata: body.metadata || {},
    };
    
    // Track the event
    await trackEvent(eventData);
    
    // Return success
    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('Error tracking event:', error);
    return NextResponse.json(
      { error: 'Failed to track event' },
      { status: 500 }
    );
  }
}
