import { NextResponse } from 'next/server';
import type { NextRequest } from 'next/server';
import { getToken } from 'next-auth/jwt';

// Cache configuration
const CACHE_REVALIDATION = {
  images: 86400, // 24 hours
  static: 31536000, // 1 year
  api: 3600, // 1 hour
};

// Rate limiting configuration
const RATE_LIMIT = {
  max: 100, // requests
  window: 60 * 1000, // 1 minute
};

// API routes that should be cached
const CACHEABLE_ROUTES = [
  '/api/portfolio',
  '/api/blog',
];

// IP-based rate limiting store
const ipAttempts = new Map<string, { count: number; timestamp: number }>();

// Get client IP from request
function getClientIP(request: NextRequest): string {
  const xff = request.headers.get('x-forwarded-for');
  return xff ? xff.split(',')[0].trim() : request.headers.get('x-real-ip') || 'unknown';
}

export async function middleware(request: NextRequest) {
  const path = request.nextUrl.pathname;
  console.log('Middleware processing path:', path);

  // Handle admin routes
  if (path.startsWith('/admin')) {
    try {
      // Import the admin auth middleware
      const { adminAuthMiddleware } = await import('./middleware/adminAuth');

      // Rate limiting for login attempts
      if (path === '/admin/login') {
        const ip = getClientIP(request);
        console.log('Processing login attempt from IP:', ip);

        const now = Date.now();

        // Clean up old attempts
        for (const [key, value] of ipAttempts.entries()) {
          if (now - value.timestamp > RATE_LIMIT.window) {
            ipAttempts.delete(key);
          }
        }

        // Check rate limit
        const attempts = ipAttempts.get(ip);
        if (attempts && attempts.count >= RATE_LIMIT.max) {
          if (now - attempts.timestamp < RATE_LIMIT.window) {
            console.log('Rate limit exceeded for IP:', ip);
            return new NextResponse(
              JSON.stringify({ error: 'Too many login attempts. Please try again later.' }),
              {
                status: 429,
                headers: {
                  'Content-Type': 'application/json',
                  'Retry-After': Math.ceil((RATE_LIMIT.window - (now - attempts.timestamp)) / 1000).toString(),
                },
              }
            );
          }
        }
      }

      // Use the admin auth middleware for all admin routes
      return adminAuthMiddleware(request);
    } catch (error) {
      console.error('Auth error in middleware:', error);
      return NextResponse.redirect(new URL('/admin/login', request.url));
    }
  }

  // For non-admin routes, just continue with the middleware

  // Handle language detection
  const supportedLanguages = ['en-US', 'sw-KE'];
  const hostname = request.headers.get('host') || 'mocky.co.ke';

  // Extract language from path if present (e.g., /en-US/about)
  const pathSegments = path.split('/').filter(Boolean);
  const pathLanguage = pathSegments.length > 0 && supportedLanguages.includes(pathSegments[0])
    ? pathSegments[0]
    : null;

  // Return response with added language headers/hreflang context
  const response = NextResponse.next();

  // Add security headers
  response.headers.set('X-DNS-Prefetch-Control', 'on');
  response.headers.set('Strict-Transport-Security', 'max-age=63072000; includeSubDomains; preload'); // 2 years
  response.headers.set('X-Frame-Options', 'SAMEORIGIN');
  response.headers.set('X-Content-Type-Options', 'nosniff');
  response.headers.set('Referrer-Policy', 'strict-origin-when-cross-origin');
  response.headers.set('X-XSS-Protection', '1; mode=block');
  response.headers.set('Permissions-Policy',
    'camera=(), microphone=(), geolocation=(), interest-cohort=(), payment=(), usb=(), magnetometer=(), accelerometer=()');

  // Set Content-Security-Policy for better protection
  response.headers.set('Content-Security-Policy',
    "default-src 'self'; " +
    "script-src 'self' 'unsafe-inline' 'unsafe-eval' https://connect.facebook.net https://www.googletagmanager.com https://www.google-analytics.com https://cdnjs.cloudflare.com https://unpkg.com; " +
    "style-src 'self' 'unsafe-inline' https://fonts.googleapis.com https://cdnjs.cloudflare.com https://unpkg.com; " +
    "img-src 'self' data: https: blob:; " +
    "font-src 'self' https://fonts.gstatic.com https://cdnjs.cloudflare.com; " +
    "connect-src 'self' https://www.google-analytics.com; " +
    "frame-src 'self' https://www.youtube.com https://player.vimeo.com; " +
    "object-src 'none';"
  );

  // Add language information in headers for debugging
  response.headers.set('X-Language', pathLanguage || 'default');

  // Handle static assets
  if (path.match(/\.(jpg|jpeg|gif|png|svg|ico|webp)$/)) {
    response.headers.set('Cache-Control', `public, max-age=${CACHE_REVALIDATION.static}, immutable`);
    return response;
  }

  // Handle API routes
  if (path.startsWith('/api/')) {
    // Add CORS headers for API routes
    response.headers.set('Access-Control-Allow-Origin', process.env.NEXT_PUBLIC_APP_URL || '*');
    response.headers.set('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
    response.headers.set('Access-Control-Allow-Headers', 'Content-Type, Authorization');

    // Handle OPTIONS requests
    if (request.method === 'OPTIONS') {
      return new NextResponse(null, { status: 204 });
    }

    // Add caching for GET requests to cacheable routes
    if (request.method === 'GET' && CACHEABLE_ROUTES.some(route => path.startsWith(route))) {
      response.headers.set('Cache-Control', `public, max-age=${CACHE_REVALIDATION.api}, stale-while-revalidate`);
    }

    // Add basic rate limiting
    const clientIP = getClientIP(request);
    const rateLimit = await getRateLimit(clientIP);

    if (rateLimit > RATE_LIMIT.max) {
      return new NextResponse(JSON.stringify({ error: 'Too many requests' }), {
        status: 429,
        headers: {
          'Content-Type': 'application/json',
          'Retry-After': '60'
        }
      });
    }
  }

  return response;
}

// Define admin authentication routes
const adminAuthRoutes = [
  '/admin',
  '/admin/login',
  '/admin/dashboard',
];

// Define admin content management routes
const adminContentRoutes = [
  '/admin/blog/:path*',
  '/admin/portfolio/:path*',
  '/admin/team/:path*',
  '/admin/scheduled-blog-posts/:path*',
  '/admin/website-portfolio/:path*',
  '/admin/categories/:path*',
  '/admin/tags/:path*',
  '/admin/authors/:path*',
];

// Define admin user management routes
const adminUserRoutes = [
  '/admin/users/:path*',
  '/admin/roles/:path*',
  '/admin/activity-logs/:path*',
];

// Define admin business routes
const adminBusinessRoutes = [
  '/admin/transactions/:path*',
  '/admin/receipts/:path*',
  '/admin/invoices/:path*',
  '/admin/quotes/:path*',
  '/admin/services/:path*',
  '/admin/pricing/:path*',
];

// Define admin settings and tools routes
const adminSettingsRoutes = [
  '/admin/settings/:path*',
  '/admin/database/:path*',
  '/admin/seo/:path*',
  '/admin/analytics/:path*',
];

// Define public routes that need middleware processing
const publicRoutes = [
  '/(|about|contact|services|portfolio|pricing|process|logos)/:path*'
];

// Combine all routes for the middleware matcher
export const config = {
  matcher: [
    // Admin auth routes
    '/admin', '/admin/login', '/admin/dashboard',
    // Admin content routes
    '/admin/blog/:path*', '/admin/portfolio/:path*', '/admin/team/:path*',
    '/admin/scheduled-blog-posts/:path*', '/admin/website-portfolio/:path*',
    '/admin/categories/:path*', '/admin/tags/:path*', '/admin/authors/:path*',
    // Admin user routes
    '/admin/users/:path*', '/admin/roles/:path*', '/admin/activity-logs/:path*',
    // Admin business routes
    '/admin/transactions/:path*', '/admin/receipts/:path*', '/admin/invoices/:path*',
    '/admin/quotes/:path*', '/admin/services/:path*', '/admin/pricing/:path*',
    // Admin settings routes
    '/admin/settings/:path*', '/admin/database/:path*', '/admin/seo/:path*', '/admin/analytics/:path*',
    // Public routes
    '/(|about|contact|services|portfolio|pricing|process|logos)/:path*'
  ]
};

// Simple in-memory rate limiting (replace with Redis in production)
const rateLimits = new Map<string, { count: number; timestamp: number }>();

async function getRateLimit(ip: string): Promise<number> {
  const now = Date.now();
  const record = rateLimits.get(ip);

  if (!record || now - record.timestamp > RATE_LIMIT.window) {
    rateLimits.set(ip, { count: 1, timestamp: now });
    return 1;
  }

  record.count += 1;
  return record.count;
}