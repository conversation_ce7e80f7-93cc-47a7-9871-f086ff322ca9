// Simple S3 connection test
require('dotenv').config();
const { S3Client, ListObjectsV2Command, PutObjectCommand } = require('@aws-sdk/client-s3');

// Hardcoded credentials for testing
const region = 'fr-par-1';
const endpoint = 'https://fr-par-1.linodeobjects.com';
const bucketName = 'mocky';
const accessKeyId = '73OQS52ORLRPBO3KG6YN';
const secretAccessKey = 'p5UW5FZ7Gog5PMP749MpdHGhPRXUKYnStFJtAaMx';

console.log('S3 Configuration:');
console.log(`  Region: ${region}`);
console.log(`  Endpoint: ${endpoint}`);
console.log(`  Bucket: ${bucketName}`);
console.log(`  Access Key: ${accessKeyId ? '✓ Set' : '✗ Not set'}`);
console.log(`  Secret Key: ${secretAccessKey ? '✓ Set' : '✗ Not set'}`);

// Create S3 client
const s3Client = new S3Client({
  region,
  endpoint,
  credentials: {
    accessKeyId,
    secretAccessKey,
  },
  forcePathStyle: true,
});

// Test connection
async function testConnection() {
  try {
    console.log(`Testing connection to S3 bucket: ${bucketName}`);

    // List objects in the bucket
    const command = new ListObjectsV2Command({
      Bucket: bucketName,
      MaxKeys: 5,
    });

    const response = await s3Client.send(command);

    console.log(`✅ Connection successful!`);
    console.log(`Found ${response.KeyCount} objects`);

    if (response.Contents && response.Contents.length > 0) {
      console.log('\nSample objects:');
      response.Contents.forEach((item, index) => {
        console.log(`  ${index + 1}. ${item.Key} (${item.Size} bytes, Last modified: ${item.LastModified})`);
      });
    }

    return true;
  } catch (error) {
    console.error(`❌ Connection failed: ${error.message}`);
    console.error(error);
    return false;
  }
}

// Create a test file in the pricing directory
async function createTestFile() {
  try {
    console.log('\nCreating test file in pricing directory...');
    const command = new PutObjectCommand({
      Bucket: bucketName,
      Key: 'images/pricing/test-file.txt',
      Body: Buffer.from('This is a test file to ensure the pricing directory exists'),
      ContentType: 'text/plain',
      ACL: 'public-read'
    });

    await s3Client.send(command);
    console.log('✅ Upload successful!');
    console.log(`Test file created at: ${endpoint}/${bucketName}/images/pricing/test-file.txt`);
    return true;
  } catch (error) {
    console.error(`❌ Upload failed: ${error.message}`);
    console.error(error);
    return false;
  }
}

// Run the tests
async function runTests() {
  const connectionSuccess = await testConnection();
  if (!connectionSuccess) {
    process.exit(1);
  }

  const uploadSuccess = await createTestFile();
  if (!uploadSuccess) {
    process.exit(1);
  }
}

runTests().catch(error => {
  console.error('Unexpected error:', error);
  process.exit(1);
});
