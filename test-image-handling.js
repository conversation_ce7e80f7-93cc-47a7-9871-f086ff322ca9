/**
 * Test script for image URL handling in the product edit page
 *
 * This script tests various image URL formats to ensure they are properly normalized
 * and displayed in the product edit page.
 */

// Define the updated normalizeImageUrl function from our utils
const normalizeImageUrl = (url) => {
  if (!url) return '/images/placeholder.jpg';

  try {
    // Log the original URL for debugging
    console.log('Normalizing URL:', url);

    // If it's already a relative URL, return as is
    if (url.startsWith('/')) {
      return url;
    }

    // Handle Linode Object Storage URLs without protocol
    if (url.includes('linodeobjects.com') && !url.startsWith('http')) {
      // Add https protocol if missing
      const normalizedUrl = `https://${url}`;
      console.log('Added protocol to URL:', normalizedUrl);
      return normalizedUrl;
    }

    // Convert HTTP to HTTPS for Linode URLs
    if (url.startsWith('http://') && url.includes('linodeobjects.com')) {
      const secureUrl = url.replace('http://', 'https://');
      console.log('Converted HTTP to HTTPS:', secureUrl);
      return secureUrl;
    }

    // Handle URLs with double slashes (except after protocol)
    if (url.includes('//')) {
      // Fix double slashes in the path portion only (not in protocol)
      const fixedUrl = url.replace(/([^:])\/\/+/g, '$1/');

      if (fixedUrl !== url) {
        console.log('Fixed double slashes in URL:', fixedUrl);
      }

      // If it's a Linode URL, also ensure it has a protocol
      if (fixedUrl.includes('linodeobjects.com') && !fixedUrl.startsWith('http')) {
        return `https://${fixedUrl}`;
      }

      return fixedUrl;
    }

    // Try to parse the URL to validate and normalize it
    try {
      const parsedUrl = new URL(url);

      // Check if it's an S3/Linode URL
      if (parsedUrl.hostname.includes('linodeobjects.com')) {
        // Fix any double slashes in the path
        const fixedUrl = url.replace(/([^:]\/)\/+/g, '$1');
        if (fixedUrl !== url) {
          console.log('Fixed S3 URL format:', fixedUrl);
        }
        return fixedUrl;
      }

      // Return the original URL for other valid URLs
      return url;
    } catch (parseError) {
      // URL parsing failed, but we can still try to fix common issues
      console.warn('URL parsing failed, attempting alternative fixes:', parseError);

      // If it looks like a domain without protocol, add https
      if (url.includes('.com') || url.includes('.net') || url.includes('.org')) {
        const withProtocol = `https://${url}`;
        console.log('Added protocol to domain-like URL:', withProtocol);
        return withProtocol;
      }

      // If it starts with a domain segment, assume it's a URL missing protocol
      if (/^[a-zA-Z0-9-]+\.[a-zA-Z0-9-]+/.test(url)) {
        const withProtocol = `https://${url}`;
        console.log('Added protocol to likely domain URL:', withProtocol);
        return withProtocol;
      }

      // If it looks like a path, return as is
      if (url.includes('/')) {
        return url;
      }
    }

    // Return the original URL if we couldn't normalize it
    return url;
  } catch (error) {
    console.error('Error normalizing image URL:', error);
    console.error('Problematic URL:', url);

    // If URL parsing fails, check if it looks like a path
    if (typeof url === 'string') {
      // If it's a Linode URL without protocol, add https
      if (url.includes('linodeobjects.com') && !url.startsWith('http')) {
        return `https://${url}`;
      }

      // Otherwise return as is if it looks like a valid path or URL
      if (url.startsWith('/') || url.startsWith('http')) {
        return url;
      }
    }

    // Return a fallback for invalid URLs
    return '/images/placeholder.jpg';
  }
};

// Test cases for URL normalization
const testUrls = [
  // Normal URLs
  'https://fr-par-1.linodeobjects.com/mocky/images/portfolio/logos/1.jpg',
  'http://fr-par-1.linodeobjects.com/mocky/images/portfolio/logos/2.jpg',

  // URLs without protocol
  'fr-par-1.linodeobjects.com/mocky/images/portfolio/logos/3.jpg',

  // URLs with double slashes
  'fr-par-1.linodeobjects.com//mocky/images/portfolio/logos/4.jpg',
  'https://fr-par-1.linodeobjects.com//mocky//images/portfolio/logos/5.jpg',

  // Relative URLs
  '/images/portfolio/logos/6.jpg',

  // Invalid URLs
  'invalid-url',
  '',
  null,
  undefined
];

// Test the normalizeImageUrl function
console.log('Testing normalizeImageUrl function:');
console.log('==================================');

testUrls.forEach((url, index) => {
  try {
    const normalizedUrl = normalizeImageUrl(url);
    console.log(`Test ${index + 1}:`);
    console.log(`  Original: ${url}`);
    console.log(`  Normalized: ${normalizedUrl}`);
    console.log('');
  } catch (error) {
    console.error(`Error normalizing URL ${url}:`, error);
  }
});

// Test the local normalizeImageUrl function from the edit page
console.log('\nTesting local normalizeImageUrl function:');
console.log('========================================');

const localNormalizeImageUrl = (url) => {
  if (!url) return '';

  // If it's already a relative URL, return as is
  if (url.startsWith('/')) {
    return url;
  }

  // Handle Linode Object Storage URLs without protocol
  if (url.includes('linodeobjects.com') && !url.startsWith('http')) {
    return `https://${url}`;
  }

  // Convert HTTP to HTTPS for Linode URLs
  if (url.startsWith('http://') && url.includes('linodeobjects.com')) {
    return url.replace('http://', 'https://');
  }

  // Handle URLs with double slashes (except after protocol)
  if (url.includes('//')) {
    // Fix double slashes in the path portion only
    const fixedUrl = url.replace(/([^:])\/\/+/g, '$1/');

    // If it's a Linode URL, also ensure it has a protocol
    if (fixedUrl.includes('linodeobjects.com') && !fixedUrl.startsWith('http')) {
      return `https://${fixedUrl}`;
    }

    return fixedUrl;
  }

  return url;
};

testUrls.forEach((url, index) => {
  try {
    const normalizedUrl = localNormalizeImageUrl(url);
    console.log(`Test ${index + 1}:`);
    console.log(`  Original: ${url}`);
    console.log(`  Normalized: ${normalizedUrl}`);
    console.log('');
  } catch (error) {
    console.error(`Error normalizing URL ${url}:`, error);
  }
});

// Test the improved onError handler for image elements
console.log('\nTesting improved onError handler for image elements:');
console.log('=================================================');

const testImageUrls = [
  'fr-par-1.linodeobjects.com/mocky/images/portfolio/logos/3.jpg',
  'http://fr-par-1.linodeobjects.com/mocky/images/portfolio/logos/2.jpg',
  'https://fr-par-1.linodeobjects.com//mocky//images/portfolio/logos/5.jpg',
  'invalid-url',
  '/images/portfolio/logos/6.jpg'
];

testImageUrls.forEach((url, index) => {
  console.log(`Test ${index + 1}:`);
  console.log(`  Original: ${url}`);

  // Simulate the improved onError handler
  let fixed = false;

  if (url.includes('linodeobjects.com')) {
    // Fix 1: Convert HTTP to HTTPS
    if (url.startsWith('http://')) {
      const fixedUrl = url.replace('http://', 'https://');
      console.log(`  Fixed (HTTP to HTTPS): ${fixedUrl}`);
      fixed = true;
    }
    // Fix 2: Add HTTPS protocol if missing
    else if (!url.startsWith('https://')) {
      const fixedUrl = `https://${url.replace(/^http:\/\//, '')}`;
      console.log(`  Fixed (Added HTTPS): ${fixedUrl}`);
      fixed = true;
    }
    // Fix 3: Fix double slashes in path
    else if (url.includes('//')) {
      const fixedUrl = url.replace(/([^:])\/\/+/g, '$1/');
      if (fixedUrl !== url) {
        console.log(`  Fixed (Double slashes): ${fixedUrl}`);
        fixed = true;
      }
    }
  }

  if (!fixed) {
    if (url.startsWith('/')) {
      console.log('  No fix needed (relative URL)');
    } else {
      console.log('  Using fallback image: /images/placeholder.jpg');
    }
  }
  console.log('');
});

console.log('Tests completed.');
