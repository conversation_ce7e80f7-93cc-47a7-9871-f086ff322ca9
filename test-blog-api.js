// Script to test the blog API directly
const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();
const axios = require('axios');

async function testBlogAPI() {
  try {
    console.log('1. Checking database records:');
    // First, check what's in the database
    const dbPosts = await prisma.blogPost.findMany({
      where: {
        status: 'published'
      }
    });
    
    console.log(`Found ${dbPosts.length} published posts in database`);
    dbPosts.forEach(post => {
      console.log(`- ${post.id}: ${post.title} (${post.status}) - Published: ${post.publishedAt}`);
    });
    
    console.log('\n2. Trying to access the API directly:');
    try {
      // Try to access the API using 0.0.0.0 instead of localhost
      const response = await axios.get('http://0.0.0.0:3000/api/blog');
      console.log(`API response status: ${response.status}`);
      console.log(`API returned ${response.data.length} posts`);
      
      if (response.data.length > 0) {
        console.log('First post from API:');
        console.log(response.data[0]);
      } else {
        console.log('API returned no posts');
      }
    } catch (apiError) {
      console.error('Error accessing API:', apiError.message);
      if (apiError.response) {
        console.log('Response status:', apiError.response.status);
        console.log('Response data:', apiError.response.data);
      }
    }
    
  } catch (error) {
    console.error('Error in test script:', error);
  } finally {
    await prisma.$disconnect();
  }
}

testBlogAPI()
  .then(() => console.log('\nTest completed!'))
  .catch(e => console.error(e)); 