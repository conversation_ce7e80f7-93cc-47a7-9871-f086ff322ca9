const fetch = require('node-fetch');
const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

// Mock Next.js API environment
global.Request = class {};
global.Response = class {
  constructor(body, init) {
    this.body = body;
    this.init = init;
    this.status = init?.status || 200;
    this.headers = new Map(Object.entries(init?.headers || {}));
  }
  
  json() {
    return Promise.resolve(JSON.parse(this.body));
  }
};

// Import API handlers
const { GET: getUsers } = require('../src/app/api/admin/users/route');
const { GET: getRoles } = require('../src/app/api/admin/roles/route');
const { GET: getLogs } = require('../src/app/api/admin/activity-logs/route');

async function main() {
  try {
    console.log('Testing API endpoints...');
    
    // Mock session for testing
    const adminUser = await prisma.user.findFirst({
      where: { username: '<EMAIL>' },
      include: { role: true }
    });
    
    if (!adminUser) {
      console.error('Admin user not found!');
      return;
    }
    
    const mockSession = {
      user: {
        id: adminUser.id,
        username: adminUser.username,
        email: adminUser.email,
        name: adminUser.name,
        role: {
          id: adminUser.role.id,
          name: adminUser.role.name,
          permissions: adminUser.role.permissions
        }
      }
    };
    
    // Mock getServerSession to return our mock session
    jest.mock('next-auth', () => ({
      getServerSession: jest.fn(() => Promise.resolve(mockSession))
    }));
    
    // Test users API
    console.log('\n--- Testing Users API ---');
    const usersResponse = await getUsers();
    console.log(`Status: ${usersResponse.status}`);
    if (usersResponse.status === 200) {
      const users = await usersResponse.json();
      console.log(`Found ${users.length} users`);
    }
    
    // Test roles API
    console.log('\n--- Testing Roles API ---');
    const rolesResponse = await getRoles();
    console.log(`Status: ${rolesResponse.status}`);
    if (rolesResponse.status === 200) {
      const roles = await rolesResponse.json();
      console.log(`Found ${roles.length} roles`);
    }
    
    // Test activity logs API
    console.log('\n--- Testing Activity Logs API ---');
    const logsResponse = await getLogs();
    console.log(`Status: ${logsResponse.status}`);
    if (logsResponse.status === 200) {
      const logsData = await logsResponse.json();
      console.log(`Found ${logsData.logs.length} logs`);
      console.log(`Total logs: ${logsData.pagination.totalCount}`);
    }
    
    console.log('\nAPI tests completed!');
  } catch (error) {
    console.error('Error testing API:', error);
  } finally {
    await prisma.$disconnect();
  }
}

main();
