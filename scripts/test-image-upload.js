/**
 * Test script for image uploads
 * 
 * This script tests the image upload functionality by:
 * 1. Uploading a test image directly to S3
 * 2. Verifying the upload was successful
 * 3. Cleaning up the test image
 * 
 * Usage: node scripts/test-image-upload.js
 */

const fs = require('fs');
const path = require('path');
const { S3Client, PutObjectCommand, GetObjectCommand, DeleteObjectCommand } = require('@aws-sdk/client-s3');
const dotenv = require('dotenv');

// Load environment variables
dotenv.config();

// Configuration
const TEST_IMAGE_PATH = path.join(__dirname, '../public/images/default-profile.jpg');
const TEST_KEY = `test/test-image-${Date.now()}.jpg`;

// Create S3 client
const s3Client = new S3Client({
  region: process.env.NEXT_PUBLIC_S3_REGION,
  endpoint: process.env.NEXT_PUBLIC_S3_ENDPOINT,
  credentials: {
    accessKeyId: process.env.NEXT_PUBLIC_S3_ACCESS_KEY,
    secretAccessKey: process.env.NEXT_PUBLIC_S3_SECRET_KEY,
  },
  forcePathStyle: true,
});

// Test configuration
console.log('Testing S3 configuration:');
console.log(`  Region: ${process.env.NEXT_PUBLIC_S3_REGION}`);
console.log(`  Endpoint: ${process.env.NEXT_PUBLIC_S3_ENDPOINT}`);
console.log(`  Bucket: ${process.env.NEXT_PUBLIC_S3_BUCKET}`);
console.log(`  Access Key: ${process.env.NEXT_PUBLIC_S3_ACCESS_KEY ? '✓ Set' : '✗ Not set'}`);
console.log(`  Secret Key: ${process.env.NEXT_PUBLIC_S3_SECRET_KEY ? '✓ Set' : '✗ Not set'}`);
console.log(`  Test Image: ${TEST_IMAGE_PATH}`);
console.log(`  Test Key: ${TEST_KEY}`);
console.log('');

async function runTests() {
  try {
    // Check if test image exists
    if (!fs.existsSync(TEST_IMAGE_PATH)) {
      console.error(`❌ Test image not found: ${TEST_IMAGE_PATH}`);
      process.exit(1);
    }

    console.log('🔍 Starting image upload tests...');
    
    // Read test image
    console.log(`📂 Reading test image: ${TEST_IMAGE_PATH}`);
    const fileContent = fs.readFileSync(TEST_IMAGE_PATH);
    console.log(`📊 Image size: ${fileContent.length} bytes`);

    // Test 1: Upload image
    console.log('\n🧪 TEST 1: Upload image to S3');
    console.log(`⏳ Uploading to ${process.env.NEXT_PUBLIC_S3_BUCKET}/${TEST_KEY}...`);
    
    const uploadStartTime = Date.now();
    try {
      const uploadCommand = new PutObjectCommand({
        Bucket: process.env.NEXT_PUBLIC_S3_BUCKET,
        Key: TEST_KEY,
        Body: fileContent,
        ContentType: 'image/jpeg',
        ACL: 'public-read',
      });
      
      await s3Client.send(uploadCommand);
      const uploadDuration = Date.now() - uploadStartTime;
      console.log(`✅ Upload successful (${uploadDuration}ms)`);
    } catch (error) {
      console.error(`❌ Upload failed: ${error.message}`);
      console.error(error);
      process.exit(1);
    }

    // Test 2: Verify upload
    console.log('\n🧪 TEST 2: Verify uploaded image');
    console.log(`⏳ Checking if ${TEST_KEY} exists...`);
    
    try {
      const getCommand = new GetObjectCommand({
        Bucket: process.env.NEXT_PUBLIC_S3_BUCKET,
        Key: TEST_KEY,
      });
      
      const { ContentLength } = await s3Client.send(getCommand);
      console.log(`✅ Image exists with size: ${ContentLength} bytes`);
    } catch (error) {
      console.error(`❌ Verification failed: ${error.message}`);
      process.exit(1);
    }

    // Test 3: Generate and verify URL
    console.log('\n🧪 TEST 3: Verify image URL');
    const imageUrl = `${process.env.NEXT_PUBLIC_S3_ENDPOINT}/${process.env.NEXT_PUBLIC_S3_BUCKET}/${TEST_KEY}`;
    console.log(`🔗 Image URL: ${imageUrl}`);
    console.log(`✅ URL generated successfully`);

    // Test 4: Clean up
    console.log('\n🧪 TEST 4: Clean up test image');
    console.log(`⏳ Deleting ${TEST_KEY}...`);
    
    try {
      const deleteCommand = new DeleteObjectCommand({
        Bucket: process.env.NEXT_PUBLIC_S3_BUCKET,
        Key: TEST_KEY,
      });
      
      await s3Client.send(deleteCommand);
      console.log(`✅ Cleanup successful`);
    } catch (error) {
      console.error(`❌ Cleanup failed: ${error.message}`);
      // Continue even if cleanup fails
    }

    console.log('\n✅ All tests completed successfully!');
  } catch (error) {
    console.error(`\n❌ Tests failed with error: ${error.message}`);
    console.error(error);
    process.exit(1);
  }
}

// Run the tests
runTests();
