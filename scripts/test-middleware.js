const fs = require('fs');

function main() {
  console.log('Testing middleware for role-based access control...');
  
  // Check if the middleware file exists
  const middlewarePath = 'src/middleware/adminAuth.ts';
  if (!fs.existsSync(middlewarePath)) {
    console.error(`Middleware file not found: ${middlewarePath}`);
    return;
  }
  
  console.log(`✅ Middleware file exists: ${middlewarePath}`);
  
  // Read the middleware file
  const middlewareContent = fs.readFileSync(middlewarePath, 'utf8');
  
  // Check for key components
  const hasRoutePermissions = middlewareContent.includes('routePermissions');
  const hasHasRoutePermission = middlewareContent.includes('hasRoutePermission');
  const hasAdminAuthMiddleware = middlewareContent.includes('adminAuthMiddleware');
  
  console.log(`✅ Route permissions mapping: ${hasRoutePermissions ? 'Present' : 'Missing'}`);
  console.log(`✅ Permission checking function: ${hasHasRoutePermission ? 'Present' : 'Missing'}`);
  console.log(`✅ Admin auth middleware function: ${hasAdminAuthMiddleware ? 'Present' : 'Missing'}`);
  
  // Check if the main middleware file imports and uses our admin auth middleware
  const mainMiddlewarePath = 'src/middleware.ts';
  if (!fs.existsSync(mainMiddlewarePath)) {
    console.error(`Main middleware file not found: ${mainMiddlewarePath}`);
    return;
  }
  
  console.log(`✅ Main middleware file exists: ${mainMiddlewarePath}`);
  
  // Read the main middleware file
  const mainMiddlewareContent = fs.readFileSync(mainMiddlewarePath, 'utf8');
  
  // Check for key components
  const importsAdminAuthMiddleware = mainMiddlewareContent.includes('adminAuthMiddleware');
  const usesAdminAuthMiddleware = mainMiddlewareContent.includes('return adminAuthMiddleware(request)');
  
  console.log(`✅ Imports admin auth middleware: ${importsAdminAuthMiddleware ? 'Yes' : 'No'}`);
  console.log(`✅ Uses admin auth middleware: ${usesAdminAuthMiddleware ? 'Yes' : 'No'}`);
  
  console.log('\nMiddleware tests completed!');
}

main();
