const fs = require('fs');
const path = require('path');

function checkFileExists(filePath) {
  return fs.existsSync(filePath);
}

function main() {
  console.log('Testing UI components...');

  // Define the components and pages to test
  const filesToCheck = [
    // User Management
    'src/app/admin/users/page.tsx',
    'src/app/admin/users/new/page.tsx',
    'src/app/admin/users/edit/[id]/page.tsx',
    'src/app/admin/users/layout.tsx',

    // Role Management
    'src/app/admin/roles/page.tsx',
    'src/app/admin/roles/new/page.tsx',
    'src/app/admin/roles/edit/[id]/page.tsx',
    'src/app/admin/roles/layout.tsx',

    // Activity Logs
    'src/app/admin/activity-logs/page.tsx',
    'src/app/admin/activity-logs/layout.tsx',

    // Components
    'src/components/admin/PermissionSelector.tsx',

    // API Routes
    'src/app/api/admin/users/route.ts',
    'src/app/api/admin/users/[id]/route.ts',
    'src/app/api/admin/roles/route.ts',
    'src/app/api/admin/roles/[id]/route.ts',
    'src/app/api/admin/activity-logs/route.ts',

    // Utilities
    'src/utils/passwordUtils.ts',
    'src/middleware/adminAuth.ts'
  ];

  // Check each file
  let allFilesExist = true;

  filesToCheck.forEach(file => {
    const exists = checkFileExists(file);
    console.log(`${exists ? '✅' : '❌'} ${file}`);

    if (!exists) {
      allFilesExist = false;
    }
  });

  if (allFilesExist) {
    console.log('\nAll UI components exist! ✅');
  } else {
    console.log('\nSome UI components are missing! ❌');
  }

  // Check if the sidebar has been updated
  const sidebarPath = 'src/components/admin/AdminSidebar.tsx';
  if (checkFileExists(sidebarPath)) {
    const sidebarContent = fs.readFileSync(sidebarPath, 'utf8');

    const hasUserManagement = sidebarContent.includes('userManagementOpen');
    const hasUserManagementItems = sidebarContent.includes('userManagementMenuItems');

    console.log(`\nSidebar includes user management state: ${hasUserManagement ? '✅' : '❌'}`);
    console.log(`Sidebar includes user management items: ${hasUserManagementItems ? '✅' : '❌'}`);
  } else {
    console.log(`\nSidebar file not found: ${sidebarPath} ❌`);
  }
}

main();
