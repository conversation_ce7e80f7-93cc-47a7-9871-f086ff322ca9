/**
 * Test script for S3 connection
 * 
 * This script tests the connection to S3 by:
 * 1. Creating an S3 client with the current configuration
 * 2. Listing the objects in the bucket
 * 
 * Usage: node scripts/test-s3-connection.js
 */

require('dotenv').config();
const { S3Client, ListObjectsV2Command } = require('@aws-sdk/client-s3');

// Configuration from environment variables
const region = process.env.NEXT_PUBLIC_S3_REGION;
const endpoint = process.env.NEXT_PUBLIC_S3_ENDPOINT;
const bucketName = process.env.NEXT_PUBLIC_S3_BUCKET;
const accessKeyId = process.env.NEXT_PUBLIC_S3_ACCESS_KEY;
const secretAccessKey = process.env.NEXT_PUBLIC_S3_SECRET_KEY;

// Display configuration
console.log('S3 Configuration:');
console.log(`  Region: ${region}`);
console.log(`  Endpoint: ${endpoint}`);
console.log(`  Bucket: ${bucketName}`);
console.log(`  Access Key: ${accessKeyId ? '✓ Set' : '✗ Not set'}`);
console.log(`  Secret Key: ${secretAccessKey ? '✓ Set' : '✗ Not set'}`);
console.log('');

// Create S3 client
const s3Client = new S3Client({
  region,
  endpoint,
  credentials: {
    accessKeyId,
    secretAccessKey,
  },
  forcePathStyle: true,
});

// Test connection
async function testConnection() {
  try {
    console.log(`Testing connection to S3 bucket: ${bucketName}`);
    
    // List objects in the bucket
    const command = new ListObjectsV2Command({
      Bucket: bucketName,
      MaxKeys: 5,
      Prefix: 'team/',
    });
    
    const response = await s3Client.send(command);
    
    console.log(`✅ Connection successful!`);
    console.log(`Found ${response.KeyCount} objects with prefix 'team/'`);
    
    if (response.Contents && response.Contents.length > 0) {
      console.log('\nSample objects:');
      response.Contents.forEach((item, index) => {
        console.log(`  ${index + 1}. ${item.Key} (${item.Size} bytes, Last modified: ${item.LastModified})`);
      });
    }
    
    return true;
  } catch (error) {
    console.error(`❌ Connection failed: ${error.message}`);
    console.error(error);
    return false;
  }
}

// Run the test
testConnection()
  .then(success => {
    if (!success) {
      process.exit(1);
    }
  })
  .catch(error => {
    console.error('Unexpected error:', error);
    process.exit(1);
  });
