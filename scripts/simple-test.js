console.log('Simple test script');

// Test if we can import the PrismaClient
try {
  const { PrismaClient } = require('@prisma/client');
  console.log('PrismaClient imported successfully');
  
  // Create a new PrismaClient instance
  const prisma = new PrismaClient();
  console.log('PrismaClient instance created');
  
  // Test a simple query
  async function testQuery() {
    try {
      console.log('Testing a simple query...');
      const count = await prisma.user.count();
      console.log(`User count: ${count}`);
      await prisma.$disconnect();
    } catch (error) {
      console.error('Error executing query:', error);
    }
  }
  
  testQuery();
} catch (error) {
  console.error('Error importing PrismaClient:', error);
}
