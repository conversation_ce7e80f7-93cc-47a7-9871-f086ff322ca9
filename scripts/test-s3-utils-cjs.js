// CommonJS version of the test script
require('dotenv').config();

const { PrismaClient } = require('@prisma/client');
const { S3Client, HeadObjectCommand } = require('@aws-sdk/client-s3');
const prisma = new PrismaClient();

async function runTests() {
  try {
    console.log('Testing S3 utilities with centralized storage configuration...');
    
    // Test 1: Get default storage config
    console.log('\nTest 1: Retrieving default storage config...');
    const config = await prisma.storageConfig.findFirst({
      where: { isDefault: true }
    });
    
    if (!config) {
      console.error('No default storage configuration found!');
      return;
    }
    
    console.log('Default storage config retrieved successfully:');
    console.log({
      id: config.id,
      provider: config.provider,
      region: config.region,
      endpoint: config.endpoint,
      bucketName: config.bucketName,
      accessKeyId: config.accessKeyId.substring(0, 4) + '...',
      isDefault: config.isDefault
    });
    
    // Test 2: Initialize an S3 client with the config
    console.log('\nTest 2: Initializing S3 client with the config...');
    const s3Client = new S3Client({
      region: config.region,
      endpoint: config.endpoint,
      credentials: {
        accessKeyId: config.accessKeyId,
        secretAccessKey: config.secretAccessKey,
      },
      forcePathStyle: true
    });
    
    console.log('S3 client initialized successfully');
    
    // Test 3: Generate a URL for an object
    console.log('\nTest 3: Generating a URL for an object...');
    const testKey = 'images/logo.png';
    const url = `${config.endpoint}/${config.bucketName}/${testKey}`;
    console.log(`Generated URL for '${testKey}': ${url}`);
    
    // Test 4: Check if an object exists
    console.log('\nTest 4: Checking if an object exists...');
    try {
      // This is a real check, but we don't expect the file to actually exist
      const normalizedKey = testKey.startsWith('/') ? testKey.slice(1) : testKey;
      
      await s3Client.send(new HeadObjectCommand({
        Bucket: config.bucketName,
        Key: normalizedKey,
      }));
      
      console.log(`Object '${testKey}' exists`);
    } catch (error) {
      console.log(`Object '${testKey}' does not exist or cannot be accessed`);
    }
    
    console.log('\nTests completed!');
    
  } catch (error) {
    console.error('Error running tests:', error);
  } finally {
    await prisma.$disconnect();
  }
}

runTests(); 