// This script requires ESM support, ensure it's run with proper Node.js configuration
// For compatibility with CommonJS, we're using require() syntax where possible

require('dotenv').config();
const fs = require('fs').promises;
const path = require('path');

async function runTests() {
  try {
    // Import modules with dynamic import (this is ESM-compatible)
    const { getDefaultStorageConfig } = await import('../src/lib/storageConfig.js');
    const { getS3Url, checkS3ObjectExists } = await import('../src/utils/s3.js');
    
    console.log('Testing S3 utilities with centralized storage configuration...');
    
    // Test 1: Get default storage config
    console.log('\nTest 1: Retrieving default storage config...');
    const config = await getDefaultStorageConfig();
    
    if (!config) {
      console.error('No default storage configuration found!');
      return;
    }
    
    console.log('Default storage config retrieved successfully:');
    console.log({
      id: config.id,
      provider: config.provider,
      region: config.region,
      endpoint: config.endpoint,
      bucketName: config.bucketName,
      accessKeyId: config.accessKeyId.substring(0, 4) + '...',
      isDefault: config.isDefault
    });
    
    // Test 2: Generate a URL for an object
    console.log('\nTest 2: Generating a URL for an object...');
    const testKey = 'images/logo.png';
    const url = await getS3Url(testKey);
    console.log(`Generated URL for '${testKey}': ${url}`);
    
    // Test 3: Check if an object exists
    console.log('\nTest 3: Checking if an object exists...');
    try {
      // This is a real check, but we don't expect the file to actually exist
      const exists = await checkS3ObjectExists(testKey);
      console.log(`Object '${testKey}' exists: ${exists}`);
    } catch (error) {
      console.log(`Error checking if object exists: ${error.message}`);
    }
    
    console.log('\nTests completed!');
    
  } catch (error) {
    console.error('Error running tests:', error);
  }
}

runTests(); 